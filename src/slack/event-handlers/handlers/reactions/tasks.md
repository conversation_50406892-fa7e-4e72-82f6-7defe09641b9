src/slack/services/ticket-creation-helper.service.ts
Comment on lines +1150 to +1158
      `${this.LOG_SPAN} [getPlatformCommentId] Looking for Slack message with TS: ${ts} in channel: ${channelId}`,
    );

    const commonWhereClause = {
      channel: { id: channelId },
      installation: { id: installation.id },
    };

    // Check if it's a direct message or threaded message
@coderabbitai coderabbitai bot 3 minutes ago
🧹 Nitpick (assertive)

📝 Log message uses internal DB id instead of Slack channel id – can confuse operators

channelId here is the database primary key (slackChannel.id), but the log wording suggests it’s the public Slack channel id.
Consider logging slackChannel.channelId (or both) to ease cross-referencing when debugging.

🤖 Prompt for AI Agents
In src/slack/services/ticket-creation-helper.service.ts around lines 1150 to
1158, the log message incorrectly logs the internal database channel ID instead
of the public Slack channel ID, which can confuse operators. Update the log
statement to include slackChannel.channelId (the public Slack channel ID)
instead of or alongside the internal channelId to improve clarity and ease
cross-referencing during debugging.
@dhruv-thena	Reply...
src/slack/services/ticket-creation-helper.service.ts
Comment on lines +775 to +782
      this.logger.debug(
        `${this.LOG_SPAN} About to process reactions for Slack message TS: ${reply.ts} in channel: ${slackChannel.channelId}`,
      );

      // Add a small delay to ensure the database transaction is fully committed
      // This prevents race conditions where we try to process reactions before
      // the reply comment is fully available in the database
      await new Promise(resolve => setTimeout(resolve, 100));
@coderabbitai coderabbitai bot 3 minutes ago
⚠️ Potential issue

⚠️ Remove the arbitrary setTimeout delay – rely on transactional guarantees instead

Introducing a hard-coded 100 ms sleep is brittle:

it makes unit tests flaky / slow,
it hides genuine race conditions instead of preventing them,
the required delay is environment-dependent (DB latency, CPU load, …).
updateMessageWithCommentId runs in its own transaction and awaits completion, so by the time control returns the commit is finished.
You can safely delete the sleep or replace it with an explicit post-commit hook/event if your ORM supports it.

-// Add a small delay to ensure the database transaction is fully committed
-// This prevents race conditions where we try to process reactions before
-// the reply comment is fully available in the database
-await new Promise(resolve => setTimeout(resolve, 100));
📝 Committable suggestion
‼️ IMPORTANT
Carefully review the code before committing. Ensure that it accurately replaces the highlighted code, contains no missing lines, and has no issues with indentation. Thoroughly test & benchmark the code to ensure it meets the requirements.

Suggested change
      this.logger.debug(
        `${this.LOG_SPAN} About to process reactions for Slack message TS: ${reply.ts} in channel: ${slackChannel.channelId}`,
      );
      // Add a small delay to ensure the database transaction is fully committed
      // This prevents race conditions where we try to process reactions before
      // the reply comment is fully available in the database
      await new Promise(resolve => setTimeout(resolve, 100));
      this.logger.debug(
        `${this.LOG_SPAN} About to process reactions for Slack message TS: ${reply.ts} in channel: ${slackChannel.channelId}`,
      );
🤖 Prompt for AI Agents
In src/slack/services/ticket-creation-helper.service.ts around lines 775 to 782,
remove the arbitrary 100ms setTimeout delay used to wait for the database
transaction to commit. Since updateMessageWithCommentId already runs in its own
transaction and awaits completion, the commit is guaranteed to be finished when
control returns. Delete the sleep entirely or replace it with an explicit
post-commit hook or event if supported by the ORM to handle post-commit logic
reliably.
@dhruv-thena	Reply...
src/slack/services/ticket-creation-helper.service.ts
Comment on lines +1107 to +1126
    if (userIds.length === 0) {
      return userInfoMap;
    }

    const results = await Promise.allSettled(
      userIds.map(async userId => {
        try {
          const userInfo = await this.slackWebAPIService.getUserInfo(
            installation.botToken,
            { user: userId },
          );
          return { userId, userInfo };
        } catch (error) {
          this.logger.error(
            `${this.LOG_SPAN} Error fetching user info for ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          return null;
        }
      })
    );
@coderabbitai coderabbitai bot 3 minutes ago
🛠️ Refactor suggestion

🚦 Batch users.info calls with a concurrency gate

Promise.allSettled(userIds.map(...getUserInfo)) fires one request per user at full throttle.
Slack’s users.info is subject to tier-based limits (20–50 req/min/workspace).
Fetching 200 users in parallel will guarantee throttling.

Reuse the p-limit guard introduced above (or a shared helper) to cap concurrency:

-import Promise from 'node:global';          // pseudo
+import pLimit from 'p-limit';
+const limit = pLimit(5);                    // reuse app-wide

-const results = await Promise.allSettled(
-  userIds.map(async userId => {
+const results = await Promise.allSettled(
+  userIds.map(userId =>
+    limit(async () => {
       const userInfo = await this.slackWebAPIService.getUserInfo(
         installation.botToken,
         { user: userId },
       );
       return { userId, userInfo };
-  })
+    }),
   )
 );
Committable suggestion skipped: line range outside the PR's diff.

🤖 Prompt for AI Agents
In src/slack/services/ticket-creation-helper.service.ts around lines 1107 to
1126, the current code calls Slack's users.info API for all userIds in parallel
without limiting concurrency, which risks hitting Slack's rate limits. To fix
this, import and use the existing p-limit concurrency guard to wrap the user
info fetch calls, ensuring only a limited number of requests run simultaneously.
Replace the Promise.allSettled call to map userIds through the p-limit wrapped
async function to batch requests with controlled concurrency.